<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\CityResource;
use App\Models\City;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Request;

class CityController
{
    public function index(): AnonymousResourceCollection
    {
        $cities = City::all();

        return CityResource::collection($cities);
    }
}
